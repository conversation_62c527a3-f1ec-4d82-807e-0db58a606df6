# API 测试功能指南

本指南介绍了 Tibco BW 转 Spring Boot CLI 工具的新增 API 测试功能，包括 Swagger 解析、测试代码生成和应用验证。

## 新增功能概述

### 1. Swagger/OpenAPI 解析
- 自动检测和解析 `swagger.json` 文件
- 提取 API 端点、参数和响应模型信息
- 支持 Swagger 2.0 和 OpenAPI 3.0 规范

### 2. API 测试代码生成
- 基于 Swagger 规范自动生成 Java 测试代码
- 支持集成测试和单元测试
- 使用 JUnit 5 和 Spring Boot Test 框架
- 包含 MockMvc 和 TestRestTemplate 测试

### 3. Spring Boot 应用启动和验证
- 自动启动 Spring Boot 应用进行测试
- 支持 Maven 和 Gradle 项目
- 健康检查和 API 一致性验证
- 应用生命周期管理

## 新增 CLI 命令

### 1. `test-api` 命令

专门用于 API 测试的完整工作流程：

```bash
# 基本用法
node dist/cli.js test-api test/_fixtures/

# 完整参数
node dist/cli.js test-api test/_fixtures/ \
  --spring-boot-project ./spring-boilerplate \
  --package com.example.movies \
  --port 8080 \
  --timeout 60 \
  --no-integration-tests \
  --no-unit-tests \
  --no-start-app
```

**参数说明：**
- `<tibco-bw-dir>`: Tibco BW 目录路径（必需）
- `--spring-boot-project`: Spring Boot 项目路径（默认：./spring-boilerplate）
- `--package`: Java 包名（默认：com.example.movies）
- `--port`: 应用启动端口（默认：8080）
- `--timeout`: 启动超时时间（秒，默认：60）
- `--no-integration-tests`: 跳过集成测试生成
- `--no-unit-tests`: 跳过单元测试生成
- `--no-start-app`: 跳过应用启动

### 2. 增强的 `auto` 命令

现有的 `auto` 命令已集成 API 测试功能：

```bash
# 完整的自动化流程（包含 API 测试）
node dist/cli.js auto test/_fixtures/

# 跳过 API 测试生成
node dist/cli.js auto test/_fixtures/ --no-test-generation

# 跳过应用启动
node dist/cli.js auto test/_fixtures/ --no-app-start

# 自定义端口
node dist/cli.js auto test/_fixtures/ --port 9090
```

**新增参数：**
- `--no-test-generation`: 跳过 API 测试代码生成
- `--no-app-start`: 跳过 Spring Boot 应用启动
- `--port`: 应用启动端口（默认：8080）

## 生成的测试文件

### 集成测试 (ApiIntegrationTest.java)

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestMethodOrder(OrderAnnotation.class)
public class ApiIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @LocalServerPort
    private int port;
    
    @Test
    @Order(1)
    @DisplayName("Test GET /movies")
    void testGetmovies() {
        // 测试实际的 HTTP 请求
        ResponseEntity<String> response = restTemplate.getForEntity(
            baseUrl + "/movies", String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
    }
}
```

### 单元测试 (MoviesControllerTest.java)

```java
@ExtendWith(MockitoExtension.class)
public class MoviesControllerTest {
    
    @Mock
    private MoviesService service;
    
    @InjectMocks
    private MoviesController controller;
    
    @Test
    @DisplayName("Test GET /movies")
    void testGetmovies() throws Exception {
        // Mock 服务层行为
        // 测试控制器逻辑
    }
}
```

## 工作流程

### 完整的自动化流程

1. **BWP 文件解析** - 解析 Tibco BW 业务流程
2. **XSD 模型生成** - 生成 Java 数据模型
3. **Java 代码生成** - 生成控制器和服务类
4. **代码部署** - 部署到 Spring Boot 项目
5. **Swagger 解析** - 解析 API 规范
6. **测试代码生成** - 生成测试类
7. **应用启动** - 启动 Spring Boot 应用
8. **API 验证** - 验证 API 一致性
9. **健康检查** - 确认应用正常运行

### 输出示例

```
🚀 Auto Tibco BW to Spring Boot conversion...
📄 Found BWP file: test/_fixtures/.../SearchMovies.bwp
📁 Auto-detected schemas directory: test/_fixtures/.../Schemas
📄 Auto-detected swagger.json: test/_fixtures/.../swagger.json
🚀 Starting Tibco BW to Spring Boot conversion...
✅ Successfully parsed BWP: SearchMovies
🏗️  Generating XSD models...
✅ Generated 36 model classes
🏗️  Generating Java code from BWP...
✅ Generated controller and service
🚀 Deploying to Spring Boot project...
✅ Deployment completed successfully
🔍 Validating API consistency...
✅ API is consistent with Swagger specification

🧪 Generating API test code...
📖 Parsed API: MovieSearch v1.0
✅ Generated 1 test cases
✅ Generated test files:
   - spring-boilerplate/src/test/java/.../ApiIntegrationTest.java
   - spring-boilerplate/src/test/java/.../MoviesControllerTest.java

🚀 Starting Spring Boot application...
✅ Application started successfully on port 8080
🏥 Running health check...
✅ Application health check passed

🎯 Next steps:
   1. Application is running at http://localhost:8080
   2. Run tests: mvn test or ./gradlew test
   3. Test API endpoints manually
   4. Stop application when done (PID: 12345)
```

## 最佳实践

### 1. 目录结构要求

确保 Tibco BW 项目具有以下结构：
```
tibco-bw-project/
├── Processes/
│   └── *.bwp
├── Schemas/
│   └── *.xsd
└── Resources/
    └── swagger.json
```

### 2. Swagger 文件要求

- 使用标准的 Swagger 2.0 或 OpenAPI 3.0 格式
- 包含完整的路径、参数和响应定义
- 确保与实际 BWP 文件中的端点一致

### 3. 测试执行

生成测试代码后，可以通过以下方式运行：

```bash
# Maven 项目
cd spring-boilerplate
mvn test

# Gradle 项目
cd spring-boilerplate
./gradlew test

# 运行特定测试类
mvn test -Dtest=ApiIntegrationTest
```

### 4. 故障排除

- **Swagger 文件未找到**: 检查文件路径和名称
- **应用启动失败**: 检查端口占用和依赖配置
- **测试失败**: 验证生成的代码和 API 端点一致性

## 配置选项

### API 测试生成选项

```typescript
interface ApiTestGenerationOptions {
  packageName: string;           // Java 包名
  outputDir: string;            // 输出目录
  testFramework: 'junit5';      // 测试框架
  useSpringBootTest: boolean;   // 使用 Spring Boot Test
  generateIntegrationTests: boolean;  // 生成集成测试
  generateUnitTests: boolean;   // 生成单元测试
  includeNegativeTests: boolean; // 包含负面测试
  baseUrl?: string;             // 基础 URL
}
```

### Spring Boot 启动配置

```typescript
interface SpringBootStartupConfig {
  projectPath: string;  // 项目路径
  port?: number;       // 端口号
  profile?: string;    // Spring Profile
  jvmArgs?: string[];  // JVM 参数
  timeout?: number;    // 超时时间（毫秒）
}
```

这些新功能使得 Tibco BW 到 Spring Boot 的转换过程更加完整和自动化，提供了端到端的测试验证能力。

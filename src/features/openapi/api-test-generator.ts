import * as fs from 'fs';
import * as path from 'path';
import { ApiTestCase, ApiTestGenerationOptions, TestParameter, ExpectedResponse } from '../../types';

/**
 * API 测试代码生成器
 * 基于 Swagger 规范生成 Java 测试代码
 */
export class ApiTestGenerator {
  private options: ApiTestGenerationOptions;

  constructor(options: ApiTestGenerationOptions) {
    this.options = options;
  }

  /**
   * 生成所有测试代码
   */
  async generateAllTests(testCases: ApiTestCase[]): Promise<{
    integrationTests: string[];
    unitTests: string[];
    generatedFiles: string[];
  }> {
    const result = {
      integrationTests: [] as string[],
      unitTests: [] as string[],
      generatedFiles: [] as string[]
    };

    // 确保输出目录存在
    const testOutputDir = path.join(this.options.outputDir, 'src', 'test', 'java', ...this.options.packageName.split('.'));
    fs.mkdirSync(testOutputDir, { recursive: true });

    if (this.options.generateIntegrationTests) {
      const integrationTestCode = this.generateIntegrationTestClass(testCases);
      const integrationTestFile = path.join(testOutputDir, 'ApiIntegrationTest.java');
      fs.writeFileSync(integrationTestFile, integrationTestCode);
      result.integrationTests.push(integrationTestCode);
      result.generatedFiles.push(integrationTestFile);
    }

    if (this.options.generateUnitTests) {
      for (const testCase of testCases) {
        const unitTestCode = this.generateUnitTestClass(testCase);
        const className = this.getControllerClassName(testCase);
        const unitTestFile = path.join(testOutputDir, `${className}Test.java`);
        fs.writeFileSync(unitTestFile, unitTestCode);
        result.unitTests.push(unitTestCode);
        result.generatedFiles.push(unitTestFile);
      }
    }

    return result;
  }

  /**
   * 生成集成测试类
   */
  private generateIntegrationTestClass(testCases: ApiTestCase[]): string {
    const className = 'ApiIntegrationTest';
    const imports = this.generateImports(true);
    const testMethods = testCases.map(testCase => this.generateIntegrationTestMethod(testCase)).join('\n\n');

    return `package ${this.options.packageName};

${imports}

/**
 * API 集成测试
 * 自动生成的测试代码，基于 Swagger 规范
 */
${this.options.useSpringBootTest ? '@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)' : '@ExtendWith(SpringExtension.class)'}
@TestMethodOrder(OrderAnnotation.class)
public class ${className} {

    ${this.options.useSpringBootTest ? '@Autowired\n    private TestRestTemplate restTemplate;\n\n    @LocalServerPort\n    private int port;' : '@Autowired\n    private MockMvc mockMvc;'}

    private String baseUrl;

    @BeforeEach
    void setUp() {
        ${this.options.useSpringBootTest ? 'baseUrl = "http://localhost:" + port;' : 'baseUrl = "";'}
    }

${testMethods}
}`;
  }

  /**
   * 生成单元测试类
   */
  private generateUnitTestClass(testCase: ApiTestCase): string {
    const controllerClassName = this.getControllerClassName(testCase);
    const testClassName = `${controllerClassName}Test`;
    const imports = this.generateImports(false);
    const testMethod = this.generateUnitTestMethod(testCase);

    return `package ${this.options.packageName};

${imports}

/**
 * ${controllerClassName} 单元测试
 * 自动生成的测试代码，基于 Swagger 规范
 */
@ExtendWith(MockitoExtension.class)
public class ${testClassName} {

    @Mock
    private ${controllerClassName.replace('Controller', 'Service')} service;

    @InjectMocks
    private ${controllerClassName} controller;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

${testMethod}
}`;
  }

  /**
   * 生成集成测试方法
   */
  private generateIntegrationTestMethod(testCase: ApiTestCase): string {
    const methodName = testCase.name;
    const httpMethod = testCase.method.toLowerCase();
    const url = this.buildTestUrl(testCase);
    const assertions = this.generateAssertions(testCase.expectedResponse);

    if (this.options.useSpringBootTest) {
      return `    @Test
    @Order(${this.getTestOrder(testCase)})
    @DisplayName("${testCase.description || `Test ${testCase.method} ${testCase.path}`}")
    void ${methodName}() {
        // Arrange
        ${this.generateTestParameters(testCase.parameters)}

        // Act
        ResponseEntity<String> response = restTemplate.${httpMethod}ForEntity(
            baseUrl + "${testCase.path}"${this.generateRestTemplateParams(testCase.parameters)},
            String.class
        );

        // Assert
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.${this.getHttpStatusName(testCase.expectedResponse.statusCode)});
        ${assertions}
    }`;
    } else {
      return `    @Test
    @Order(${this.getTestOrder(testCase)})
    @DisplayName("${testCase.description || `Test ${testCase.method} ${testCase.path}`}")
    void ${methodName}() throws Exception {
        // Act & Assert
        mockMvc.perform(${httpMethod}("${testCase.path}")${this.generateMockMvcParams(testCase.parameters)})
                .andExpect(status().is${this.getHttpStatusName(testCase.expectedResponse.statusCode).toLowerCase()}())
                ${this.generateMockMvcAssertions(testCase.expectedResponse)};
    }`;
    }
  }

  /**
   * 生成单元测试方法
   */
  private generateUnitTestMethod(testCase: ApiTestCase): string {
    const methodName = testCase.name;
    const serviceMethodName = this.getServiceMethodName(testCase);
    const mockSetup = this.generateMockSetup(testCase);
    const methodCall = this.generateControllerMethodCall(testCase);

    return `    @Test
    @DisplayName("${testCase.description || `Test ${testCase.method} ${testCase.path}`}")
    void ${methodName}() throws Exception {
        // Arrange
        ${mockSetup}

        // Act
        ${methodCall}

        // Assert
        verify(service).${serviceMethodName}(${this.generateServiceCallParams(testCase.parameters)});
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.${this.getHttpStatusName(testCase.expectedResponse.statusCode)});
    }`;
  }

  /**
   * 生成导入语句
   */
  private generateImports(isIntegrationTest: boolean): string {
    const commonImports = [
      'import org.junit.jupiter.api.*;',
      'import org.springframework.test.context.junit.jupiter.SpringExtension;',
      'import org.junit.jupiter.api.extension.ExtendWith;',
      'import static org.assertj.core.api.Assertions.*;'
    ];

    if (isIntegrationTest) {
      if (this.options.useSpringBootTest) {
        return [
          ...commonImports,
          'import org.springframework.boot.test.context.SpringBootTest;',
          'import org.springframework.boot.test.web.client.TestRestTemplate;',
          'import org.springframework.boot.test.web.server.LocalServerPort;',
          'import org.springframework.beans.factory.annotation.Autowired;',
          'import org.springframework.http.*;'
        ].join('\n');
      } else {
        return [
          ...commonImports,
          'import org.springframework.test.web.servlet.MockMvc;',
          'import org.springframework.test.web.servlet.setup.MockMvcBuilders;',
          'import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;',
          'import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;'
        ].join('\n');
      }
    } else {
      return [
        ...commonImports,
        'import org.mockito.Mock;',
        'import org.mockito.InjectMocks;',
        'import org.mockito.junit.jupiter.MockitoExtension;',
        'import org.springframework.test.web.servlet.MockMvc;',
        'import org.springframework.test.web.servlet.setup.MockMvcBuilders;',
        'import static org.mockito.Mockito.*;',
        'import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;',
        'import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;'
      ].join('\n');
    }
  }

  /**
   * 生成测试参数
   */
  private generateTestParameters(parameters: TestParameter[]): string {
    return parameters
      .map(param => `${this.getJavaType(param.type)} ${param.name} = ${this.formatTestValue(param.value, param.type)};`)
      .join('\n        ');
  }

  /**
   * 获取 Java 类型
   */
  private getJavaType(type: string): string {
    switch (type.toLowerCase()) {
      case 'string':
        return 'String';
      case 'integer':
        return 'Integer';
      case 'number':
        return 'Double';
      case 'boolean':
        return 'Boolean';
      case 'array':
        return 'List<String>';
      default:
        return 'String';
    }
  }

  /**
   * 格式化测试值
   */
  private formatTestValue(value: any, type: string): string {
    if (type === 'string' || type === 'String') {
      return `"${value}"`;
    }
    return String(value);
  }

  /**
   * 生成断言
   */
  private generateAssertions(expectedResponse: ExpectedResponse): string {
    const assertions = ['assertThat(response.getBody()).isNotNull();'];
    
    if (expectedResponse.properties && expectedResponse.properties.length > 0) {
      assertions.push('// TODO: Add specific property assertions based on response schema');
    }

    return assertions.join('\n        ');
  }

  /**
   * 获取控制器类名
   */
  private getControllerClassName(testCase: ApiTestCase): string {
    // 从路径推断控制器名称
    const pathSegments = testCase.path.split('/').filter(segment => segment && !segment.startsWith('{'));
    const resourceName = pathSegments[0] || 'Api';
    return this.toPascalCase(resourceName) + 'Controller';
  }

  /**
   * 获取服务方法名
   */
  private getServiceMethodName(testCase: ApiTestCase): string {
    const method = testCase.method.toLowerCase();
    const resource = testCase.path.split('/')[1] || 'resource';
    return method + this.toPascalCase(resource);
  }

  /**
   * 转换为 PascalCase
   */
  private toPascalCase(str: string): string {
    return str
      .replace(/[^a-zA-Z0-9]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  /**
   * 获取 HTTP 状态名称
   */
  private getHttpStatusName(statusCode: number): string {
    const statusMap: Record<number, string> = {
      200: 'OK',
      201: 'CREATED',
      204: 'NO_CONTENT',
      400: 'BAD_REQUEST',
      401: 'UNAUTHORIZED',
      403: 'FORBIDDEN',
      404: 'NOT_FOUND',
      500: 'INTERNAL_SERVER_ERROR'
    };
    return statusMap[statusCode] || 'OK';
  }

  /**
   * 获取测试顺序
   */
  private getTestOrder(testCase: ApiTestCase): number {
    // GET 请求优先级最高，POST 次之
    const methodPriority: Record<string, number> = {
      'GET': 1,
      'POST': 2,
      'PUT': 3,
      'DELETE': 4
    };
    return methodPriority[testCase.method] || 5;
  }

  // 辅助方法（需要根据具体需求实现）
  private buildTestUrl(testCase: ApiTestCase): string {
    return testCase.path;
  }

  private generateRestTemplateParams(parameters: TestParameter[]): string {
    return '';
  }

  private generateMockMvcParams(parameters: TestParameter[]): string {
    const queryParams = parameters
      .filter(p => p.location === 'query')
      .map(p => `.param("${p.name}", "${p.value}")`)
      .join('');
    return queryParams;
  }

  private generateMockMvcAssertions(expectedResponse: ExpectedResponse): string {
    return '.andExpect(content().contentType(MediaType.APPLICATION_JSON))';
  }

  private generateMockSetup(testCase: ApiTestCase): string {
    return '// Mock service behavior here';
  }

  private generateControllerMethodCall(testCase: ApiTestCase): string {
    return 'ResponseEntity<?> result = controller.handleRequest();';
  }

  private generateServiceCallParams(parameters: TestParameter[]): string {
    return parameters.map(p => `any(${this.toPascalCase(p.type)}.class)`).join(', ');
  }
}
